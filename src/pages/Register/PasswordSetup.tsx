import React from 'react';
import { Form, Input, Button, message, Checkbox } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';
import { useAuthStore } from '@/store';
import { ApiService } from '@/services';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import PasswordRequirements from '@/components/PasswordRequirements';

const PasswordSetup: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, clearFormData } = useRegisterStore();
  const { login } = useAuthStore();
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);
  const [password, setPassword] = React.useState('');
  const [agreeTerms, setAgreeTerms] = React.useState(false);

  const onPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);
  };

  const onFinish = async (values: { password: string; confirm: string }) => {
    if (!agreeTerms) {
      message.error(t('auth.register.step4.form.agreeTermsRequired'));
      return;
    }

    const finalData = {
      ...formData,
      password: values.password,
    };

    setLoading(true);
    try {
      // const response = await ApiService.post('/auth/register', finalData);
      // if (response.data.token) {
      //   // 注册成功后自动登录
      //   const permissions = [];
      //   login(response.data.user, response.data.token, permissions);
      //   message.success(t('auth.register.success'));
      //   clearFormData(); // 清空注册数据
      //   navigate('/');
      // } else {
      //   message.error(response.message || t('auth.register.error'));
      // }
    } catch (error: any) {
      message.error(error?.response?.data?.message || t('auth.register.error'));
    } finally {
      setLoading(false);
    }
  };

  // 检查是否有前面步骤的数据，如果没有则重定向
  React.useEffect(() => {
    if (!formData.email) {
      message.warning(
        t('auth.register.step4.messages.emailVerificationRequired')
      );
      navigate('/register/email');
    } else if (!formData.alias) {
      message.warning(t('auth.register.step4.messages.aliasRequired'));
      navigate('/register/alias');
    } else if (!formData.firstName || !formData.lastName) {
      message.warning(t('auth.register.step4.messages.personalInfoRequired'));
      navigate('/register/personal-info');
    }
  }, [formData, navigate, t]);

  return (
    <>
      <h1 className="font-arial mb-13 text-center text-[32px] text-[#ff5e13] font-bold">
        {t('auth.register.step4.title')}
      </h1>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        size="large"
      >
        <Form.Item
          label={
            <span className="text-base text-gray-700 font-medium">
              {t('auth.register.step4.form.password')}:
            </span>
          }
          name="password"
          rules={[
            {
              required: true,
              message: t('auth.register.step4.form.passwordRequired'),
            },
            {
              min: 8,
              message: t(
                'auth.register.step4.form.passwordRequirements.length'
              ),
            },
          ]}
          className="mb-4"
        >
          <Input.Password
            placeholder={t('auth.register.step4.form.passwordPlaceholder')}
            onChange={onPasswordChange}
            iconRender={visible =>
              visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
            }
            className="placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)"
          />
        </Form.Item>

        {/* 密码要求列表 */}
        <PasswordRequirements password={password} />

        <Form.Item
          name="confirm"
          label={
            <span className="text-base text-gray-700 font-medium">
              {t('auth.register.step4.form.confirmPassword')}:
            </span>
          }
          dependencies={['password']}
          rules={[
            {
              required: true,
              message: t('auth.register.step4.form.confirmPasswordRequired'),
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error(t('auth.register.step4.form.passwordMismatch'))
                );
              },
            }),
          ]}
          className="mb-6"
        >
          <Input.Password
            placeholder={t(
              'auth.register.step4.form.confirmPasswordPlaceholder'
            )}
            iconRender={visible =>
              visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
            }
            className="placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)"
          />
        </Form.Item>

        {/* 服务条款复选框 */}
        <div className="mb-6">
          <Checkbox
            checked={agreeTerms}
            onChange={e => setAgreeTerms(e.target.checked)}
            className="text-sm"
          >
            {t('auth.register.step4.form.agreeTerms')}{' '}
            <a href="#" className="text-blue-600 hover:text-blue-700">
              {t('auth.register.step4.form.termsOfService')}
            </a>{' '}
            {t('auth.register.step4.form.and')}{' '}
            <a href="#" className="text-blue-600 hover:text-blue-700">
              {t('auth.register.step4.form.privacyPolicy')}
            </a>
          </Checkbox>
        </div>

        <Button
          type="primary"
          htmlType="submit"
          size="large"
          loading={loading}
          disabled={!agreeTerms}
          className="[&.ant-btn-variant-solid:disabled]:opacity-45 h-[63px] w-full"
        >
          {t('auth.register.step4.form.signUp')}
        </Button>

        {/* <FormButton text={t('auth.register.step4.buttons.next')} /> */}
      </Form>
    </>
  );
};

export default PasswordSetup;
